<template>
  <div>
    <div class="banner">
      <div class="banner-content">
        <img src="@/assets/images/vip/bg_title.png" alt="" class="banner-title" />
        <div class="main-wrap">
          <div class="app-tabs">
            <div class="flex flex-ai-center">
              <div
                @click="handleTab(index)"
                v-for="(item, index) in tabsList"
                :key="index"
                class="app-tabs-item"
                :class="{ 'app-tabs-active': index == activeTab }"
              >
                {{ item }}
              </div>
            </div>
          </div>
          <div class="membership">
            <div class="membership-item">
              <img src="@/assets/images/vip/content.png" alt="" class="membership-item-img" />
              <div class="h-title">服务项目</div>
              <div class="membership-item-subitem">标准检索</div>
              <div class="membership-item-subitem">高级标准检索</div>
            </div>
            <div class="membership-item">
              <div class="membership-item-subitem gradient-gay f-bold">非会员用户</div>
              <div class="membership-item-subitem h-154 flex flex-center">免费</div>
              <div class="membership-item-subitem">
                <img src="@/assets/images/vip/none.png" alt="" class="membership-item-icon" />
              </div>
              <div class="membership-item-subitem">
                <img src="@/assets/images/vip/have.png" alt="" class="membership-item-icon" />
              </div>
            </div>
            <div class="membership-item hover-person">
              <img src="@/assets/images/vip/person.png" alt="" class="membership-item-hover" />
              <div class="membership-item-subitem gradient-blue f-bold">个人用户</div>
              <div class="membership-item-subitem h-154">
                <div>
                  <div class="f-16 c-99">
                    <span class="f-36 c-primary f-bold">600</span>
                    /年
                  </div>
                  <div class="f-14 c-99">
                    原价：
                    <span class="line-through">400元</span>
                  </div>
                  <div class="membership-item-subitem-btn">立即开通</div>
                </div>
              </div>
              <div class="membership-item-subitem">20条</div>
              <div class="membership-item-subitem">20条</div>
            </div>
            <div class="membership-item">
              <div class="membership-item-subitem gradient-blue f-bold">企业用户</div>
              <div class="flex">
                <div class="membership-item flex-1 hover-firm">
                  <div class="membership-item-subitem pl-0 h-154 flex flex-center bt-none">
                    <div>
                      <div class="f-16 c-99">
                        <span class="f-36 c-primary f-bold">600</span>
                        /年
                      </div>
                      <div class="f-14 c-99">
                        原价：
                        <span class="line-through">400元</span>
                      </div>
                      <div class="membership-item-subitem-btn">立即开通</div>
                    </div>
                  </div>
                  <div class="membership-item-subitem pl-0">20条</div>
                  <div class="membership-item-subitem pl-0">20条</div>
                </div>
                <div class="membership-item flex-1 hover-firm">
                  <div class="membership-item-subitem pl-0 h-154 flex flex-center bt-none">
                    <div>
                      <div class="f-16 c-99">
                        <span class="f-36 c-primary f-bold">600</span>
                        /年
                      </div>
                      <div class="f-14 c-99">
                        原价：
                        <span class="line-through">400元</span>
                      </div>
                      <div class="membership-item-subitem-btn">立即开通</div>
                    </div>
                  </div>
                  <div class="membership-item-subitem pl-0">20条</div>
                  <div class="membership-item-subitem pl-0">20条</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content main-wrap"></div>
    <BxcLogin v-if="openLogin" v-model:open="openLogin" @success="handleClick" />
  </div>
</template>

<script setup lang="ts">
  // useHead({
  //   title: '',
  //   meta: [
  //     { name: 'keywords', content: '' },
  //     {
  //       name: 'description',
  //       content: '',
  //     },
  //   ],
  // });

  import { handleJump } from '@/utils/common';
  import { useUserStore } from '@/store/userStore';

  const userStore = useUserStore();

  const openLogin: Ref<boolean> = ref(false);
  const tabsList: Ref<string[]> = ref(['VIP会员', '权益包会员']);
  const activeTab: Ref<number> = ref(0);

  const handleTab = (index: number) => {
    activeTab.value = index;
  };

  const handleClick = () => {
    if (!userStore.token) {
      openLogin.value = true;
    } else {
      openLogin.value = false;
      handleJump('/user-center/trusteeship/index');
    }
  };

  const toList = async () => {
    await navigateTo({
      path: '/retrieval/domestic',
    });
  };
</script>

<style lang="scss" scoped>
  .banner {
    position: relative;
    width: 100%;
    min-height: 1140px;
    background: url('@/assets/images/vip/bg_image.png') no-repeat center;
    background-size: 100% 100%;

    &-content {
      position: absolute;
      top: 140px;
      left: 0;
      right: 0;
      bottom: 0;
    }

    &-title {
      display: block;
      margin: 0 auto;
      height: 47px;
    }

    .main-wrap {
      margin-top: 37px;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      overflow: hidden;
      border-image: linear-gradient(0deg, #ffffff, #c2ddff) 10 10;

      .app-tabs {
        height: 80px;
        background: #e8f3ff;
        padding: 0 25px;
        box-sizing: border-box;

        &-item {
          font-size: 22px;
          font-weight: bold;
          color: #333;

          &:hover {
            color: $primary-color;
          }

          &:nth-child(n + 2) {
            margin-left: 75px;
          }
        }
      }
    }

    .membership {
      display: flex;
      padding: 35px 25px;
      box-sizing: border-box;
      background: #fff;

      &-item {
        position: relative;

        &:first-child .membership-item-subitem {
          padding-left: 25px;
          background-color: #f8fbff;
        }

        &:not(:first-child) .membership-item-subitem {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &:nth-child(n + 2) .membership-item-subitem {
          border-left: none !important;
        }

        &:nth-child(2),
        &:nth-child(3) {
          width: 180px;
        }

        &:nth-child(4) {
          flex: 1;
          border-top-right-radius: 5px;
          overflow: hidden;
        }

        &-img {
          display: block;
          height: 140px;
        }

        &-subitem {
          font-size: 14px;
          color: #333;
          height: 54px;
          line-height: 51px;
          border: 1px solid #ddecf8;
          box-sizing: border-box;

          &:not(:first-child) {
            border-top: none !important;
          }

          &-btn {
            color: #fff;
            width: 111px;
            height: 34px;
            line-height: 34px;
            text-align: center;
            background: linear-gradient(-90deg, #ae00ff, #045cff);
            box-shadow: 0px 4px 3px 0px rgba(24, 53, 108, 0.26);
            border-radius: 5px;
            margin-top: 13px;
            cursor: pointer;
          }
        }

        &-icon {
          width: 20px;
          height: 20px;
        }

        &-hover {
          height: 60px;
          position: absolute;
          top: -30px;
          left: calc((100% - 60px) / 2);
        }
      }
    }
  }

  .is-active {
    color: $primary-color;
  }

  .h-title {
    height: 68px;
    line-height: 68px;
    background-color: #f8fbff;
    padding-left: 25px;
    border-left: 1px solid #ddecf8;
    border-right: 1px solid #ddecf8;
    border-bottom: 1px solid #ddecf8;
    box-sizing: border-box;

    &::before {
      content: '';
      position: absolute;
      top: calc(50% - 10px);
      left: 13px;
      width: 4px;
      height: 20px;
      background: $primary-color;
    }
  }

  .gradient {
    &-gay {
      background: linear-gradient(0deg, #ffffff 0%, #ececec 100%);
    }

    &-blue {
      background: linear-gradient(0deg, #ffffff 0%, #dbe8ff 100%);
    }
  }

  .h-154 {
    height: 154px !important;
    line-height: normal !important;
    text-align: center;
  }

  .pl-0 {
    padding-left: 0 !important;
    background-color: #fff !important;
  }

  .bt-none {
    border-top: none !important;
  }

  .hover {
    &-person:hover {
      background: #fff !important;
    }

    &-firm:hover {
    }
  }

  .line-through {
    text-decoration: line-through;
  }
</style>
